generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String              @id @default(cuid())
  name          String?
  email         String              @unique
  emailVerified DateTime?
  image         String?
  password      String?
  phone         String?
  role          UserRole            @default(BUYER)
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
  accounts      Account[]
  organizer     Organizer?
  sessions      Session[]
  tickets       Ticket[]
  transactions  Transaction[]
  validator     Validator?
}

model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?
  access_token             String?
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?
  session_state            String?
  refresh_token_expires_in Int?
  user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Organizer {
  id           String                 @id @default(cuid())
  userId       String                 @unique
  orgName      String
  legalName    String?
  npwp         String?
  socialMedia  Json?
  verified     Boolean                @default(false)
  createdAt    DateTime               @default(now())
  updatedAt    DateTime               @updatedAt
  bankAccount  BankAccount?
  crew         Crew[]
  events       Event[]
  user         User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  verification OrganizerVerification?
  vouchers     Voucher[]
  withdrawals  Withdrawal[]
}

model Event {
  id             String        @id @default(cuid())
  slug           String        @unique
  organizerId    String
  title          String
  description    String?
  posterUrl      String?
  bannerUrl      String?
  posterPublicId String?
  bannerPublicId String?
  category       String?
  venue          String
  address        String?
  city           String?
  province       String
  country        String
  tags           String[]
  images         String[]
  imagePublicIds String[]
  featured       Boolean       @default(false)
  seatingMap     String?
  maxAttendees   Int?
  website        String?
  terms          String?
  startDate      DateTime
  endDate        DateTime
  status         EventStatus   @default(DRAFT)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  crew           Crew[]
  organizer      Organizer     @relation(fields: [organizerId], references: [id])
  ticketTypes    TicketType[]
  transactions   Transaction[]
  vouchers       Voucher[]
}

model TicketType {
  id                String             @id @default(cuid())
  eventId           String
  name              String
  description       String?
  price             Decimal            @db.Decimal(10, 2)
  currency          String             @default("IDR")
  quantity          Int
  sold              Int                @default(0)
  reserved          Int                @default(0)
  maxPerPurchase    Int                @default(10)
  isVisible         Boolean            @default(true)
  allowTransfer     Boolean            @default(false)
  ticketFeatures    String?
  perks             String?
  earlyBirdDeadline DateTime?
  saleStartDate     DateTime?
  saleEndDate       DateTime?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  orderItems        OrderItem[]
  tickets           Ticket[]
  reservations      TicketReservation[]
  event             Event              @relation(fields: [eventId], references: [id], onDelete: Cascade)
}

model Transaction {
  id               String        @id @default(cuid())
  userId           String
  eventId          String
  amount           Decimal       @db.Decimal(10, 2)
  currency         String        @default("IDR")
  paymentMethod    String
  paymentReference String?
  invoiceNumber    String        @unique
  status           PaymentStatus @default(PENDING)
  details          Json?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  orderItems       OrderItem[]
  payments         Payment[]
  tickets          Ticket[]
  buyerInfo        BuyerInfo?
  event            Event         @relation(fields: [eventId], references: [id])
  user             User          @relation(fields: [userId], references: [id])
}

model OrderItem {
  id           String      @id @default(cuid())
  orderId      String
  ticketTypeId String
  quantity     Int
  price        Decimal     @db.Decimal(10, 2)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  order        Transaction @relation(fields: [orderId], references: [id], onDelete: Cascade)
  ticketType   TicketType  @relation(fields: [ticketTypeId], references: [id])
}

model Payment {
  id              String        @id @default(cuid())
  orderId         String
  gateway         String
  amount          Decimal       @db.Decimal(10, 2)
  status          PaymentStatus @default(PENDING)
  paymentId       String?
  hmacSignature   String?
  callbackPayload Json?
  receivedAt      DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  order           Transaction   @relation(fields: [orderId], references: [id])
}

model Approval {
  id          String         @id @default(cuid())
  entityType  String
  entityId    String
  reviewerId  String?
  status      ApprovalStatus @default(PENDING)
  notes       String?
  reviewedAt  DateTime?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  submittedAt DateTime?
  submitterId String?
}

model Log {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  entity    String?
  entityId  String?
  metadata  Json?
  timestamp DateTime @default(now())
}

model Validator {
  id        String   @id @default(cuid())
  userId    String   @unique
  eventIds  String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model BankAccount {
  id            String    @id @default(cuid())
  organizerId   String    @unique
  bankName      String
  accountName   String
  accountNumber String
  branch        String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  organizer     Organizer @relation(fields: [organizerId], references: [id], onDelete: Cascade)
}

model Withdrawal {
  id          String           @id @default(cuid())
  organizerId String
  amount      Float
  currency    String           @default("IDR")
  status      WithdrawalStatus @default(PENDING)
  reference   String?
  notes       String?
  processedAt DateTime?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  organizer   Organizer        @relation(fields: [organizerId], references: [id])
}

model Crew {
  id           String    @id @default(cuid())
  organizerId  String
  eventId      String
  name         String
  role         String
  email        String?
  phone        String?
  idCardNumber String    @unique
  barcode      String    @unique
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  event        Event     @relation(fields: [eventId], references: [id])
  organizer    Organizer @relation(fields: [organizerId], references: [id])
}

model Voucher {
  id                String       @id @default(cuid())
  organizerId       String
  eventId           String?
  code              String       @unique
  discountType      DiscountType
  discountValue     Float
  maxUsage          Int
  usedCount         Int          @default(0)
  minPurchaseAmount Float?
  startDate         DateTime
  endDate           DateTime
  isActive          Boolean      @default(true)
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  event             Event?       @relation(fields: [eventId], references: [id])
  organizer         Organizer    @relation(fields: [organizerId], references: [id])
}

model Ticket {
  id             String        @id @default(cuid())
  ticketTypeId   String
  transactionId  String
  userId         String
  qrCode         String        @unique
  status         TicketStatus  @default(ACTIVE)
  checkedIn      Boolean       @default(false)
  checkInTime    DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  imagePublicId  String?
  imageUrl       String?
  // Digital ticket fields (formerly ETicket)
  fileUrl        String?
  filePublicId   String?
  delivered      Boolean       @default(false)
  deliveredAt    DateTime?
  scannedAt      DateTime?
  ticketHolder   TicketHolder?
  ticketType     TicketType    @relation(fields: [ticketTypeId], references: [id], onDelete: Cascade)
  transaction    Transaction   @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model OrganizerVerification {
  id                String             @id @default(cuid())
  organizerId       String             @unique
  ktpNumber         String?
  ktpName           String?
  ktpAddress        String?
  ktpImageUrl       String?
  ktpImagePublicId  String?
  npwpNumber        String?
  npwpName          String?
  npwpAddress       String?
  npwpImageUrl      String?
  npwpImagePublicId String?
  termsAccepted     Boolean            @default(false)
  termsAcceptedAt   DateTime?
  status            VerificationStatus @default(PENDING)
  submittedAt       DateTime?
  reviewedAt        DateTime?
  reviewedBy        String?
  rejectionReason   String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  organizer         Organizer          @relation(fields: [organizerId], references: [id], onDelete: Cascade)
}

model BuyerInfo {
  id             String      @id @default(cuid())
  transactionId  String      @unique
  fullName       String
  identityType   String      // KTP, Passport, etc.
  identityNumber String
  email          String
  whatsapp       String
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  transaction    Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
}

model TicketHolder {
  id             String   @id @default(cuid())
  ticketId       String   @unique
  fullName       String
  identityType   String   // KTP, Passport, etc.
  identityNumber String
  email          String
  whatsapp       String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  ticket         Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)
}

model TicketReservation {
  id           String            @id @default(cuid())
  sessionId    String            // For guest tracking
  ticketTypeId String
  quantity     Int
  reservedAt   DateTime          @default(now())
  expiresAt    DateTime
  status       ReservationStatus @default(ACTIVE)
  metadata     Json?             // Additional reservation data
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  ticketType   TicketType        @relation(fields: [ticketTypeId], references: [id], onDelete: Cascade)

  @@index([expiresAt])
  @@index([sessionId])
  @@index([status])
}

enum UserRole {
  ADMIN
  ORGANIZER
  BUYER
}

enum EventStatus {
  DRAFT
  PENDING_REVIEW
  PUBLISHED
  REJECTED
  COMPLETED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  EXPIRED
  REFUNDED
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum TicketStatus {
  ACTIVE
  USED
  CANCELLED
  EXPIRED
  REFUNDED
}

enum WithdrawalStatus {
  PENDING
  PROCESSING
  COMPLETED
  CANCELLED
  FAILED
}

enum DiscountType {
  PERCENTAGE
  FIXED_AMOUNT
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ReservationStatus {
  ACTIVE
  EXPIRED
  CONVERTED
  CANCELLED
}
