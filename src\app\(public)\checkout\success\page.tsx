"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { CheckCircle, Download, ArrowLeft, Ticket } from "lucide-react";
import Link from "next/link";

export default function CheckoutSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("orderId");

  const [orderData, setOrderData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!orderId) {
      setError("Order ID is missing");
      setLoading(false);
      return;
    }

    const fetchOrderData = async () => {
      try {
        const response = await fetch(`/api/public/orders/${orderId}`);
        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || "Failed to load order");
        }

        // Transform the API response to match the expected format
        const transformedData = {
          id: result.data.id,
          invoiceNumber: result.data.invoiceNumber,
          status: result.data.status,
          amount: result.data.amount,
          currency: result.data.currency || "IDR",
          event: {
            title: result.data.event.title,
            date: result.data.event.formattedStartDate || "TBA",
            location:
              [
                result.data.event.venue,
                result.data.event.city,
                result.data.event.province,
              ]
                .filter(Boolean)
                .join(", ") || "TBA",
          },
          tickets: result.data.items.map((item: any) => ({
            id: item.id,
            type: item.ticketType.name,
            quantity: item.quantity,
          })),
          isTestMode: process.env.NEXT_PUBLIC_XENDIT_ENABLED !== "true",
        };

        setOrderData(transformedData);
      } catch (err: any) {
        console.error("Error fetching order:", err);
        setError(err.message || "Failed to load order details");
      } finally {
        setLoading(false);
      }
    };

    fetchOrderData();
  }, [orderId]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(price);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="text-muted-foreground mt-2">
              Loading order details...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !orderData) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertDescription>
            {error || "Failed to load order details"}
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => router.push("/")}>Back to Home</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mx-auto max-w-2xl">
        {/* Success Header */}
        <Card className="mb-6">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-green-600">
              {orderData.isTestMode
                ? "Test Payment Successful!"
                : "Payment Successful!"}
            </CardTitle>
            <p className="text-muted-foreground">
              {orderData.isTestMode
                ? "Your test transaction has been completed successfully."
                : "Your payment has been processed successfully."}
            </p>
          </CardHeader>
        </Card>

        {/* Test Mode Alert */}
        {orderData.isTestMode && (
          <Alert className="mb-6">
            <AlertDescription>
              <strong>Test Mode:</strong> This was a test transaction. No actual
              payment was processed.
            </AlertDescription>
          </Alert>
        )}

        {/* Order Details */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Order Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-muted-foreground text-sm">Order ID</p>
                <p className="font-medium">{orderData.id}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">Invoice Number</p>
                <p className="font-medium">{orderData.invoiceNumber}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">Total Amount</p>
                <p className="font-medium text-green-600">
                  {formatPrice(orderData.amount)}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">Status</p>
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  {orderData.status}
                </span>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="mb-2 font-medium">Event Details</h4>
              <p className="font-medium">{orderData.event.title}</p>
              <p className="text-muted-foreground text-sm">
                {orderData.event.date}
              </p>
              <p className="text-muted-foreground text-sm">
                {orderData.event.location}
              </p>
            </div>

            <div className="border-t pt-4">
              <h4 className="mb-2 font-medium">Tickets</h4>
              <div className="space-y-2">
                {orderData.tickets.map((ticket: any) => (
                  <div key={ticket.id} className="flex justify-between">
                    <span>{ticket.type}</span>
                    <span>{ticket.quantity}x</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Link href="/tickets" className="block">
            <Button className="w-full" size="lg">
              <Ticket className="mr-2 h-4 w-4" />
              View My Tickets
            </Button>
          </Link>

          <Button
            variant="outline"
            className="w-full"
            size="lg"
            onClick={() => {
              // TODO: Implement invoice download functionality
              alert("Invoice download feature coming soon!");
            }}
          >
            <Download className="mr-2 h-4 w-4" />
            Download Invoice
          </Button>

          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => router.push("/")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>

            <Link href="/orders" className="flex-1">
              <Button variant="outline" className="w-full">
                View All Orders
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
